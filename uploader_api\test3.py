import pandas as pd
import csv

# === CONFIG ===
SHOP_ID = "0706/6071/8813"  # <<< REPLACE with your real shop ID
style_gram_file = "data.xlsx"
template_file = "products_export.csv"
output_file = "polished_dc_huggies_10K.csv"
bodyHeader = "10K Polished DC Huggies"
# Names
styleHeader = "Style Number"
gramHeader = "Gram"
colorHeader = "Color"

def format_handle(style):
    return style.strip().lower().replace(" ", "-")

def generate_image_url(style_number):
    return f"https://cdn.shopify.com/s/files/1/{SHOP_ID}/files/{style_number}.png"

def create_shopify_export_from_template(style_gram_file, template_file, output_file):
    # Read template CSV (only first row for default values)
    with open(template_file, mode='r', encoding='utf-8-sig') as f:
        reader = csv.DictReader(f)
        default_row = next(reader)
        fieldnames = reader.fieldnames

    # Read style_gram Excel
    df = pd.read_excel(style_gram_file, dtype=str).fillna("")

    # Prepare new rows
    new_rows = []

    
    

    for _, row in df.iterrows():
        style = row.get(styleHeader, "").strip()
        gram = row.get(gramHeader, "").strip()
        color = "Yellow" if row.get(colorHeader, "").strip() == "" else row.get(colorHeader, "").strip()
        if not style or not gram:
            continue  # skip incomplete rows

        # Clone default and override fields
        new_row = default_row.copy()
        new_row["Handle"] = format_handle(style)
        new_row["Title"] = style
        new_row["Body (HTML)"] = bodyHeader
        new_row["Variant SKU"] = style
        new_row["Option2 Value"] = gram
        new_row["Option3 Value"] = color
        new_row["Image Src"] = generate_image_url(style)
        new_row["Color (product.metafields.shopify.color-pattern)"] = color

        new_rows.append(new_row)

    # Write to final output
    with open(output_file, mode='w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(new_rows)

    print(f"✅ Done: Exported {len(new_rows)} products to → {output_file}")

# === Run ===
create_shopify_export_from_template(style_gram_file, template_file, output_file)
