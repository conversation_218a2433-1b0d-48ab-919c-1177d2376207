import os
import re
from openpyxl import load_workbook, Workbook
from openpyxl.utils import get_column_letter, column_index_from_string
from PIL import Image
import io

# === CONFIGURATION ===
input_excel = "10K LOBSTER LOCKS (FINDING).xlsx"  # Use the .xlsx file
output_folder = "10K LOBSTER LOCKS (FINDING)"
output_excel = "10K LOBSTER LOCKS (FINDING) color_with_size.xlsx"
start_cell = "A3"
end_cell ="E15"

# === SETUP OUTPUT FOLDER ===
os.makedirs(output_folder, exist_ok=True)

# === LOAD THE WORKBOOK ===
wb = load_workbook(input_excel)
ws = wb.active

# === MAP IMAGES TO ANCHOR CELLS ===
image_map = {}
for image in ws._images:
    # Get cell anchor for each image
    anchor = image.anchor._from
    col_letter = get_column_letter(anchor.col + 1)
    row_number = anchor.row + 1
    cell = f"{col_letter}{row_number}"
    image_map[cell] = image

# === EXTRACT START/END COORDINATES ===
start_col = column_index_from_string(start_cell[0])
end_col = column_index_from_string(end_cell[0])
start_row = int(start_cell[1:])
end_row = int(end_cell[1:])

# === FUNCTION TO EXTRACT STYLE AND SIZE ===
def extract_style_and_size(style_text):
    """
    Extract style number and size from text like 'F8527 (7x16m)'
    Returns: (clean_style, size)
    """
    if not style_text:
        return "", ""
    
    style_text = str(style_text).strip()
    
    # Look for content in parentheses
    match = re.search(r'(.+?)\s*\(([^)]+)\)', style_text)
    if match:
        clean_style = match.group(1).strip()
        size = match.group(2).strip()
        return clean_style, size
    else:
        # No parentheses found, return original as style with empty size
        return style_text, ""

# === COLLECT STYLES, SIZES, GRAMS, AND COLORS ===
style_data_list = []

for col in range(start_col, end_col + 1):
    col_letter = get_column_letter(col)
    row = start_row
    while row <= end_row:
        image_cell = f"{col_letter}{row}"
        style_cell = f"{col_letter}{row + 1}"
        gram_cell = f"{col_letter}{row + 2}"

        style_no = ws[style_cell].value
        gram = ws[gram_cell].value
        print(f"Processing: {style_no}, {gram}")

        if image_cell in image_map and style_no:
            # Extract clean style and size
            clean_style, size = extract_style_and_size(style_no)
            
            print(f"Original: {style_no} -> Style: {clean_style}, Size: {size}")

            # Color Logic (based on clean style)
            if "WG" in clean_style:
                color = "White"
            elif "PG" in clean_style:
                color = "Pink"
            else:
                color = "Yellow"

            # Save Image with clean style name
            img_bytes = image_map[image_cell]._data()
            image = Image.open(io.BytesIO(img_bytes))
            image_filename = f"{clean_style}.png"
            image.save(os.path.join(output_folder, image_filename))
            print(f"Saved image: {image_filename}")

            # Append collected data
            style_data_list.append((clean_style, size, gram, color))

        row += 3  # Move to next group

# === WRITE OUTPUT EXCEL ===
wb_out = Workbook()
ws_out = wb_out.active
ws_out.title = "Style Data"
ws_out.append(["Style Number", "Size", "Gram", "Color"])  # Header row

for style, size, gram, color in style_data_list:
    ws_out.append([style, size, gram, color])

wb_out.save(output_excel)

print(f"✅ Done. Processed {len(style_data_list)} items:")
print(f"   - Saved {len(style_data_list)} images to '{output_folder}' folder")
print(f"   - Created '{output_excel}' with Style Number, Size, Gram, and Color columns")
print("\nSample data:")
for i, (style, size, gram, color) in enumerate(style_data_list[:3]):
    print(f"   {i+1}. Style: {style}, Size: {size}, Gram: {gram}, Color: {color}")
