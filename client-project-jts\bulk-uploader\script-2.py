import requests
import os
import pandas as pd
import base64  # Import base64 for image encoding
import math

# Load the product data from CSV
file_path = './test.csv'
df = pd.read_csv(file_path)

# Shopify credentials
SHOPIFY_API_KEY = '73ffa4f66b36e4d82c8c9b8520756157'
SHOPIFY_PASSWORD = 'db9f61753fc3a81eb7fde07e03e7eaea'
SHOPIFY_STORE = 'jts-inc-jewelry.myshopify.com'
API_VERSION = '2023-04'  # Use the latest API version


# Base URL for API calls
shopify_url = f"https://{SHOPIFY_API_KEY}:{SHOPIFY_PASSWORD}@{SHOPIFY_STORE}/admin/api/{API_VERSION}/products.json"
print(shopify_url)

# Directory where images are stored
image_directory = './images/'  # Update this with the actual image directory path

# Function to clean NaN values
def clean_nan(value, default_value=""):
    if isinstance(value, float) and math.isnan(value):
        return default_value
    return value

# Function to create a product with variants and an image
def create_product(row):
    title = clean_nan(row['Title'])
    handle = clean_nan(row['Handle'])
    vendor = clean_nan(row['Vendor'])
    product_type = clean_nan(row['Type'])
    tags = clean_nan(row['Tags'])
    inventory_qty = clean_nan(row['Variant Inventory Qty'], 0)  # Default to 0 if NaN
    
    # Prepare the product data
    product_data = {
        "product": {
            "title": title,
            "handle": handle,
            "vendor": vendor,
            "product_type": product_type,
            "tags": tags,
            "variants": [
                {
                    "option1": clean_nan(row['Option1 Value']),  # Karat (14K)
                    "option2": clean_nan(row['Option2 Value']),  # Weight (N/A)
                    "option3": clean_nan(row['Option3 Value']),  # Color (yellow)
                    "inventory_quantity": inventory_qty,
                    "sku": clean_nan(row['Variant SKU'])
                }
            ],
            "options": [
                {"name": clean_nan(row['Option1 Name'])},  # Option 1: Karat
                {"name": clean_nan(row['Option2 Name'])},  # Option 2: Weight
                {"name": clean_nan(row['Option3 Name'])}   # Option 3: Color
            ]
        }
    }
    
    # Match the image based on the product Title (image should be named with the Title)
    image_path = os.path.join(image_directory, f"{title}.jpg")  # Assuming images are in jpg format
    if not os.path.exists(image_path):
        image_path = os.path.join(image_directory, f"{title}.png")  # Check for png format if jpg is not found
    
    if os.path.exists(image_path):
        with open(image_path, 'rb') as image_file:
            # Read and encode the image as base64
            image_data = base64.b64encode(image_file.read()).decode('utf-8')
            product_data['product']['images'] = [{
                "attachment": image_data  # Attach image as base64 encoded string
            }]
    
    # Send the POST request to create the product
    response = requests.post(shopify_url, json=product_data)
    
    if response.status_code == 201:
        print(f"Product '{title}' created successfully!")
    else:
        print(f"Failed to create product '{title}': {response.text}")

# Loop through the dataframe and create products
for index, row in df.iterrows():
    create_product(row)
