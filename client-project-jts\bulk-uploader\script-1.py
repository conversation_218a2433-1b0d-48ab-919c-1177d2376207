import requests
import os
import pandas as pd
import base64  # Import base64 for image encoding

# Load the product data from CSV
file_path = './test.csv'
df = pd.read_csv(file_path)

# Shopify credentials
SHOPIFY_API_KEY = '73ffa4f66b36e4d82c8c9b8520756157'
SHOPIFY_PASSWORD = 'db9f61753fc3a81eb7fde07e03e7eaea'
SHOPIFY_STORE = 'jts-inc-jewelry.myshopify.com'
API_VERSION = '2023-04'  # Use the latest API version

# Base URL for API calls
shopify_url = f"https://{SHOPIFY_API_KEY}:{SHOPIFY_PASSWORD}@{SHOPIFY_STORE}/admin/api/{API_VERSION}/products.json"

# Directory where images are stored
image_directory = './images/'  # Update this with the actual image directory path

# Function to create a product with variants and an image
def create_product(row):
    title = row['Title']
    handle = row['Handle']
    vendor = row['Vendor']
    product_type = row['Type']
    tags = row['Tags']
    inventory_qty = row['Variant Inventory Qty']
    
    # Prepare the product data
    product_data = {
        "product": {
            "title": title,
            "handle": handle,
            "vendor": vendor,
            "product_type": product_type,
            "tags": tags,
            "variants": [
                {
                    "option1": row['Option1 Value'],  # Karat (14K)
                    "option2": row['Option2 Value'],  # Weight (N/A)
                    "option3": row['Option3 Value'],  # Color (yellow)
                    "inventory_quantity": inventory_qty,
                    "sku": row['Variant SKU']
                }
            ],
            "options": [
                {"name": row['Option1 Name']},  # Option 1: Karat
                {"name": row['Option2 Name']},  # Option 2: Weight
                {"name": row['Option3 Name']}   # Option 3: Color
            ]
        }
    }
    
    # Match the image based on the product Title (image should be named with the Title)
    image_path = os.path.join(image_directory, f"{title}.jpg")  # Assuming images are in jpg format
    if not os.path.exists(image_path):
        image_path = os.path.join(image_directory, f"{title}.png")  # Check for png format if jpg is not found
    
    if os.path.exists(image_path):
        with open(image_path, 'rb') as image_file:
            # Read and encode the image as base64
            image_data = base64.b64encode(image_file.read()).decode('utf-8')
            product_data['product']['images'] = [{
                "attachment": image_data  # Attach image as base64 encoded string
            }]
    
    # Send the POST request to create the product
    response = requests.post(shopify_url, json=product_data)
    
    if response.status_code == 201:
        print(f"Product '{title}' created successfully!")
    else:
        print(f"Failed to create product '{title}': {response.text}")

# Loop through the dataframe and create products
for index, row in df.iterrows():
    create_product(row)
