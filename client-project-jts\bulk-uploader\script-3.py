import requests
import base64
import os
import csv

# Shopify Admin API Access Token
SHOPIFY_ACCESS_TOKEN = '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'  # Replace with your actual Admin API Access Token
SHOPIFY_STORE = 'jts-inc-jewelry.myshopify.com'  # Replace with your actual store name
API_VERSION = '2024-10'  # Use the latest API version or the one supported by your app

# Base URL for API calls
shopify_url = f"https://{SHOPIFY_STORE}/admin/api/{API_VERSION}/products.json"

def create_product(row):
    print("Name : ",row['Title'])
    image_file_path = f"./images/{row['Title']}.jpg"  # Assuming image names match product titles with .jpg extension
    with open(image_file_path, "rb") as image_file:
        image_data = base64.b64encode(image_file.read()).decode('utf-8')

    product_data = {
        "product": {
            "title": row['Title'],
            "vendor": row['Vendor'],
            "product_type": row['Type'],
            "tags": row['Tags'],
            "variants": [
                {
                    "option1": row['Option1 Value'],  # Karat
                    "option2": row['Option2 Value'],  # Weight
                    "option3": row['Option3 Value'],  # Color
                    "price": row['Variant Price'],
                    "inventory_quantity": row['Variant Inventory Qty']
                }
            ],
            "images": [
                {
                    "attachment": image_data,  # Attach image as base64
                    "position": 1
                }
            ]
        }
    }

    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": SHOPIFY_ACCESS_TOKEN  # Use the Admin API Access Token
    }

    response = requests.post(shopify_url, json=product_data, headers=headers)
    if response.status_code == 201:
        print(f"Product '{row['Title']}' created successfully.")
    else:
        print(f"Failed to create product '{row['Title']}': {response.text}")

# Example for reading and creating products from a CSV file
with open('./test.csv', mode='r', encoding='utf-8-sig') as file:
    reader = csv.DictReader(file)
    for row in reader:
        create_product(row)
