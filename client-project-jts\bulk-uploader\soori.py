import shopify

# Shopify Admin API Access Token
SHOPIFY_ACCESS_TOKEN = '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'
SHOPIFY_STORE = 'jts-inc-jewelry.myshopify.com'
API_VERSION = '2024-07'  

shop_url = SHOPIFY_STORE
api_version = API_VERSION
private_app_password = SHOPIFY_ACCESS_TOKEN

session = shopify.Session(shop_url, api_version, private_app_password)
shopify.ShopifyResource.activate_session(session)

print(session)

shop = shopify.Shop.current()
print(shop)


product = shopify.Product()
product.title = "Test Product"
product.id                          
product.save()                

print(shopify.Product.exists(product.id))

shopify.ShopifyResource.clear_session()