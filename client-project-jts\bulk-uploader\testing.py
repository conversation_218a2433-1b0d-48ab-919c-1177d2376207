import requests

SHOPIFY_ACCESS_TOKEN = '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'  # Replace with your actual Admin API Access Token
SHOPIFY_STORE = 'jts-inc-jewelry.myshopify.com'
API_VERSION = '2024-10'

def test_graphql_introspection():
    url = f"https://{SHOPIFY_STORE}/admin/api/{API_VERSION}/graphql.json"
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": SHOPIFY_ACCESS_TOKEN
    }
    query = '''
    {
      __schema {
        queryType {
          name
        }
      }
    }
    '''
    
    try:
        response = requests.post(url, json={"query": query}, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
    except requests.RequestException as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_graphql_introspection()