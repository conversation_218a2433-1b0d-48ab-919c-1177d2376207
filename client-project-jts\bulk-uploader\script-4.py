import requests
import base64
import csv
from typing import Dict, Any

# Shopify Admin API Access Token
SHOPIFY_ACCESS_TOKEN = '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'  # Replace with your actual Admin API Access Token
SHOPIFY_STORE = 'jts-inc-jewelry.myshopify.com'  # Replace with your actual store name
API_VERSION = '2024-10'  # Use the latest API version or the one supported by your app

# GraphQL endpoint
graphql_url = f"https://{SHOPIFY_STORE}/admin/api/{API_VERSION}/graphql.json"

def create_product(row: Dict[str, Any]) -> None:
    print(f"Creating product: {row['Title']}")
    image_file_path = f"./images/{row['Title']}.jpg"  # Assuming image names match product titles with .jpg extension
    
    with open(image_file_path, "rb") as image_file:
        image_data = base64.b64encode(image_file.read()).decode('utf-8')

    # GraphQL mutation for creating a product
    mutation = """
    mutation createProduct($input: ProductInput!) {
      productCreate(input: $input) {
        product {
          id
          title
        }
        userErrors {
          field
          message
        }
      }
    }
    """

    # Prepare the variables for the GraphQL mutation
    variables = {
        "input": {
            "title": row['Title'],
            "vendor": row['Vendor'],
            "productType": row['Type'],
            "tags": row['Tags'].split(','),  # Assuming tags are comma-separated
            "variants": [
                {
                    "options": [row['Option1 Value'], row['Option2 Value'], row['Option3 Value']],
                    "price": row['Variant Price'],
                    "inventoryQuantities": {
                        "availableQuantity": int(row['Variant Inventory Qty']),
                    }
                }
            ],
            "images": [
                {
                    "altText": row['Title'],
                    "src": f"data:image/jpeg;base64,{image_data}"
                }
            ]
        }
    }

    # Headers for the GraphQL request
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": SHOPIFY_ACCESS_TOKEN
    }

    # Make the GraphQL request
    response = requests.post(graphql_url, json={"query": mutation, "variables": variables}, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('data', {}).get('productCreate', {}).get('product'):
            print(f"Product '{row['Title']}' created successfully.")
        else:
            print(f"Failed to create product '{row['Title']}': {result.get('data', {}).get('productCreate', {}).get('userErrors')}")
    else:
        print(f"Failed to create product '{row['Title']}': {response.text}")

# Read and create products from a CSV file
def main():
    with open('./test.csv', mode='r', encoding='utf-8-sig') as file:
        reader = csv.DictReader(file)
        for row in reader:
            create_product(row)

if __name__ == "__main__":
    main()