import pandas as pd
import csv

# === CONFIG ===
data_file = "data.xlsx"  # Input data file
output_file = "processed_data.csv"  # Output file

# Column headers
styleHeader = "Style Number"
gramHeader = "Gram"
colorHeader = "Color"

def read_and_process_data(input_file, output_file):
    """
    Read style number, gram, and color data from Excel file.
    Apply default values and filtering rules:
    - Color: Fill "Yellow" if missing
    - Gram: Fill "N/A" if missing  
    - Skip rows where Style Number is None/empty
    """
    
    try:
        # Read Excel file
        print(f"📖 Reading data from: {input_file}")
        df = pd.read_excel(input_file, dtype=str)
        
        # Replace NaN with empty strings for easier processing
        df = df.fillna("")
        
        print(f"📊 Total rows in input file: {len(df)}")
        
        # Process data
        processed_rows = []
        skipped_count = 0
        
        for index, row in df.iterrows():
            # Get style number and check if it's valid
            style = row.get(styleHeader, "").strip()
            
            # Skip rows where style number is None, empty, or "None"
            if not style or style.lower() == "none":
                skipped_count += 1
                print(f"⏭️  Skipping row {index + 1}: Style Number is empty or None")
                continue
            
            # Get gram value, fill "N/A" if missing
            gram = row.get(gramHeader, "").strip()
            if not gram or gram.lower() == "none":
                gram = "N/A"
            
            # Get color value, fill "Yellow" if missing
            color = row.get(colorHeader, "").strip()
            if not color or color.lower() == "none":
                color = "Yellow"
            
            # Create processed row
            processed_row = {
                "Style Number": style,
                "Gram": gram,
                "Color": color
            }
            
            processed_rows.append(processed_row)
            print(f"✅ Processed row {index + 1}: Style={style}, Gram={gram}, Color={color}")
        
        # Write to CSV
        if processed_rows:
            print(f"\n💾 Writing {len(processed_rows)} processed rows to: {output_file}")
            
            with open(output_file, mode='w', newline='', encoding='utf-8') as f:
                fieldnames = ["Style Number", "Gram", "Color"]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(processed_rows)
            
            print(f"✅ Successfully processed {len(processed_rows)} rows")
            print(f"⏭️  Skipped {skipped_count} rows (empty/None style numbers)")
            print(f"📄 Output saved to: {output_file}")
        else:
            print("❌ No valid rows to process!")
            
    except FileNotFoundError:
        print(f"❌ Error: File '{input_file}' not found!")
    except Exception as e:
        print(f"❌ Error processing file: {str(e)}")

def display_summary(output_file):
    """Display a summary of the processed data"""
    try:
        df = pd.read_csv(output_file)
        print(f"\n📊 SUMMARY OF PROCESSED DATA:")
        print(f"Total processed records: {len(df)}")
        print(f"Records with 'N/A' gram: {len(df[df['Gram'] == 'N/A'])}")
        print(f"Records with 'Yellow' color: {len(df[df['Color'] == 'Yellow'])}")
        print(f"Unique colors: {df['Color'].unique()}")
        print(f"Unique grams: {df['Gram'].unique()}")
        
        # Show first few rows
        print(f"\n📋 First 5 processed records:")
        print(df.head().to_string(index=False))
        
    except FileNotFoundError:
        print(f"❌ Output file '{output_file}' not found for summary!")
    except Exception as e:
        print(f"❌ Error displaying summary: {str(e)}")

# === Main execution ===
if __name__ == "__main__":
    print("🚀 Starting data processing...")
    
    # Process the data
    read_and_process_data(data_file, output_file)
    
    # Display summary
    display_summary(output_file)
    
    print("\n🎉 Data processing complete!")
