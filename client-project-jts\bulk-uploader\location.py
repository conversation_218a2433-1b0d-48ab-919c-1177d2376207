import requests

# Shopify Admin API Access Token
SHOPIFY_ACCESS_TOKEN = '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'  # Replace with your actual Admin API Access Token
SHOPIFY_STORE = 'jts-inc-jewelry.myshopify.com'  # Replace with your actual store name
API_VERSION = '2024-10'  # Use the latest API version or the one supported by your app

# GraphQL endpoint
graphql_url = f"https://{SHOPIFY_STORE}/admin/api/{API_VERSION}/graphql.json"

def query_locations():
    # GraphQL query for fetching locations
    query = """
    {
      locations(first: 10) {
        edges {
          node {
            id
            name
            address {
              address1
              city
              country
            }
          }
        }
      }
    }
    """

    # Headers for the GraphQL request
    headers = {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": SHOPIFY_ACCESS_TOKEN
    }

    # Make the GraphQL request
    response = requests.post(graphql_url, json={"query": query}, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        locations = result.get('data', {}).get('locations', {}).get('edges', [])
        if locations:
            print("Locations found:")
            for location in locations:
                node = location['node']
                print(f"ID: {node['id']}")
                print(f"Name: {node['name']}")
                print(f"Address: {node['address']['address1']}, {node['address']['city']}, {node['address']['country']}")
                print("---")
        else:
            print("No locations found.")
    else:
        print(f"Failed to fetch locations: {response}")

if __name__ == "__main__":
    query_locations()